/**
 * sidebar-navbar.css - Styles pour la sidebar et navbar moderne
 */

/* Variables - Style Sigma Élégant */
:root {
    /* Couleurs Sigma */
    --sigma-primary: #0F0F23;     /* Bleu nuit profond */
    --sigma-secondary: #1A1A2E;   /* Bleu nuit moyen */
    --sigma-tertiary: #16213E;    /* Bleu ardoise */
    --sigma-accent: #0E4B99;      /* Bleu royal */
    --sigma-accent-light: #2E86AB; /* Bleu ciel */
    --sigma-gold: #F39C12;        /* Or élégant */
    --sigma-gold-light: #F7DC6F;  /* Or clair */
    --sigma-silver: #BDC3C7;      /* Argent */
    --sigma-white: #FFFFFF;
    --sigma-light: #F8F9FA;

    /* Couleurs d'accent sobres */
    --accent-blue: #2E86AB;
    --accent-dark: #0E4B99;
    --accent-light: #5BA3C7;
    --accent-subtle: #7BB3D1;
    --accent-muted: #9BC4DB;
    --accent-soft: #BDD5E5;

    /* Texte */
    --text-primary: #F1F5F9;
    --text-secondary: #CBD5E1;
    --text-muted: #94A3B8;
    --text-dark: #475569;

    /* Dégradés Sigma */
    --sigma-gradient-main: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
    --sigma-gradient-accent: linear-gradient(135deg, #0E4B99 0%, #2E86AB 100%);
    --sigma-gradient-gold: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
    --sigma-gradient-neon: linear-gradient(135deg, #00D4FF 0%, #8A2BE2 100%);
    --sigma-gradient-cyber: linear-gradient(135deg, #FF1493 0%, #8A2BE2 50%, #00D4FF 100%);
    --sigma-gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Ombres Sigma */
    --sigma-shadow-soft: 0 4px 20px rgba(15, 15, 35, 0.3);
    --sigma-shadow-medium: 0 8px 32px rgba(15, 15, 35, 0.4);
    --sigma-shadow-strong: 0 12px 48px rgba(15, 15, 35, 0.6);
    --sigma-shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
    --sigma-shadow-gold: 0 0 20px rgba(243, 156, 18, 0.3);

    /* Tailles */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --navbar-height: 75px;

    /* Bordures */
    --card-radius: 20px;
    --button-radius: 15px;
    --input-radius: 12px;
    --badge-radius: 10px;

    /* Animations */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Styles de base */
body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Structure de l'application */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
    background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="%239C92AC" fill-opacity="0.05" fill-rule="evenodd"/%3E%3C/svg%3E');
}

/* Sidebar Sigma */
.app-sidebar {
    width: var(--sidebar-width);
    background: var(--sigma-gradient-main);
    box-shadow: var(--sigma-shadow-strong);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-medium) cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 100;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    border-right: 2px solid var(--sigma-accent);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);

}

/* Effet de lueur pour sidebar */
.app-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--sigma-gradient-glass);
    pointer-events: none;
}

/* Animation de particules pour sidebar */
.app-sidebar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, var(--neon-blue) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, var(--sigma-gold) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, var(--neon-purple) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 80px 80px;
    opacity: 0.1;
    animation: particleFloat 20s infinite linear;
    pointer-events: none;
}

.sidebar-collapsed .app-sidebar {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid var(--sigma-accent);
    position: relative;
    z-index: 2;

}

/* Effet de lueur en bas pour sidebar header */
.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--sigma-gradient-neon);
    box-shadow: 0 0 10px var(--neon-blue);
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.3rem;
    color: var(--text-primary);
    position: relative;
    z-index: 2;
}

.logo i {
    margin-right: 0.8rem;
    font-size: 1.5rem;
    color: var(--sigma-accent-light);
}

.sidebar-collapsed .logo span {
    display: none;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background-color: var(--bg-color);
}

.sidebar-collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: 2rem 0;
    position: relative;
    z-index: 2;
}

.sidebar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 0.5rem;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-medium);
    border-radius: var(--button-radius);
    margin: 0 1rem;
    position: relative;
    overflow: hidden;
    font-weight: 500;

}

/* Effet de survol pour les liens du menu */
.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--sigma-gradient-accent);
    transition: left var(--transition-medium);
    z-index: -1;
}

.sidebar-menu a:hover {
    color: var(--text-primary);
    background-color: rgba(46, 134, 171, 0.15);
}

.sidebar-menu a:hover::before {
    left: 0;
}

.sidebar-menu li.active a {
    background-color: var(--sigma-accent);
    color: var(--text-primary);
    font-weight: 600;
}



.sidebar-menu i {
    margin-right: 1rem;
    font-size: 1.4rem;
    transition: all var(--transition-medium);
}

.sidebar-menu a:hover i {
    color: var(--neon-blue);
    filter: drop-shadow(0 0 5px var(--neon-blue));
    transform: scale(1.1);
}

.sidebar-collapsed .sidebar-menu span {
    display: none;
}

.sidebar-collapsed .sidebar-menu a {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar-collapsed .sidebar-menu i {
    margin-right: 0;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
}

.avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: var(--sigma-gradient-neon);
    color: var(--sigma-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    border: 2px solid var(--neon-blue);
    box-shadow: var(--sigma-shadow-neon);
    transition: all var(--transition-medium);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--sigma-gradient-cyber);
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 0 25px var(--neon-blue);
    animation: neonGlow 2s ease-in-out infinite;
}

.avatar:hover::before {
    opacity: 0.3;
}

.sidebar-collapsed .user-details {
    display: none;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

.user-email {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Content Wrapper */
.content-wrapper {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar-collapsed .content-wrapper {
    margin-left: var(--sidebar-collapsed-width);
}

/* Navbar Sigma */
.app-navbar {
    height: var(--navbar-height);
    background: var(--sigma-gradient-main);
    box-shadow: var(--sigma-shadow-medium);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 90;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 2px solid var(--sigma-accent);

    /* Effet de lueur en bas */
    position: relative;
}

.app-navbar::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--sigma-gradient-neon);
    box-shadow: 0 0 15px var(--neon-blue);
}

.navbar-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    margin-right: 1rem;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.menu-toggle:hover {
    background-color: var(--bg-color);
}

.page-title {
    font-weight: 600;
    font-size: 1.3rem;
    color: var(--text-primary);
}

.navbar-right {
    display: flex;
    align-items: center;
}

.navbar-item {
    margin-left: 1.5rem;
    position: relative;
    cursor: pointer;
}

.navbar-item i {
    color: var(--text-secondary);
    font-size: 1.25rem;
}

.user-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    min-width: 200px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    display: none;
    z-index: 100;
}

.user-dropdown.open .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: var(--bg-color);
}

.dropdown-item i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--text-secondary);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    background-color: var(--bg-color);
}

/* Cards */
.card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--card-radius);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(226, 232, 240, 0.7);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: rgba(226, 232, 240, 0.9);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.card-title {
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--text-primary);
    margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .app-sidebar {
        transform: translateX(-100%);
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 100;
    }

    .sidebar-mobile-open .app-sidebar {
        transform: translateX(0);
    }

    .content-wrapper {
        margin-left: 0 !important;
    }

    .menu-toggle {
        display: flex;
    }

    .sidebar-toggle {
        display: none;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

/* Dashboard specific styles */
.welcome-section {
    text-align: center;
    margin-bottom: 2.5rem;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border-radius: var(--card-radius);
    color: white;
    box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.25), 0 10px 10px -5px rgba(99, 102, 241, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    animation: pulse 15s infinite linear;
    z-index: 1;
}

.welcome-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(0,0,0,0.1), rgba(0,0,0,0));
    z-index: 1;
}

.welcome-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.95;
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
}

.admin-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
}

.admin-card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--card-radius);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
    padding: 2rem;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(226, 232, 240, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.admin-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 30px -12px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: rgba(226, 232, 240, 0.9);
}

.admin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    z-index: 1;
}

.admin-card.annotateurs::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--info-color));
}



.admin-card-icon {
    width: 70px;
    height: 70px;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.75rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.1), 0 4px 6px -2px rgba(99, 102, 241, 0.05);
}

.admin-card:hover .admin-card-icon {
    transform: scale(1.05);
    box-shadow: 0 15px 20px -3px rgba(99, 102, 241, 0.15), 0 8px 10px -2px rgba(99, 102, 241, 0.1);
}

.admin-card-icon.annotateurs {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    box-shadow: 0 10px 15px -3px rgba(6, 182, 212, 0.1), 0 4px 6px -2px rgba(6, 182, 212, 0.05);
}

.admin-card:hover .admin-card-icon.annotateurs {
    box-shadow: 0 15px 20px -3px rgba(6, 182, 212, 0.15), 0 8px 10px -2px rgba(6, 182, 212, 0.1);
}



.admin-card-icon i {
    font-size: 2rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.admin-card:hover .admin-card-icon i {
    transform: scale(1.1);
}

.admin-card-icon.annotateurs i {
    color: var(--secondary-color);
}



.admin-card-title {
    font-size: 1.35rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    position: relative;
}

.admin-card-text {
    color: var(--text-secondary);
    margin-bottom: 1.75rem;
    flex: 1;
    line-height: 1.6;
}

.admin-card-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
    position: relative;
}

.admin-card-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: currentColor;
    transition: width 0.3s ease;
}

.admin-card-link:hover::after {
    width: 100%;
}

.admin-card-link.annotateurs {
    color: var(--secondary-color);
}



.admin-card-link:hover {
    color: var(--primary-dark);
}

.admin-card-link.annotateurs:hover {
    color: var(--secondary-dark);
}



.admin-card-link i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.admin-card-link:hover i {
    transform: translateX(8px);
}

/* Animation pour le fond de la section de bienvenue */
@keyframes pulse {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Footer */
.footer {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 1.25rem 0;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    z-index: 10;
}

/* Animations Sigma */
@keyframes particleFloat {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
    }
    100% {
        transform: translateY(0) rotate(360deg);
    }
}

@keyframes logoPulse {
    0%, 100% {
        text-shadow: 0 0 10px var(--neon-blue);
    }
    50% {
        text-shadow: 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 0.5;
        transform: translateY(-50%) scale(1.2);
    }
}

@keyframes neonGlow {
    0%, 100% {
        box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue);
    }
    50% {
        box-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes cyberpunkGlow {
    0%, 100% {
        filter: drop-shadow(0 0 5px var(--neon-blue));
    }
    25% {
        filter: drop-shadow(0 0 10px var(--neon-purple));
    }
    50% {
        filter: drop-shadow(0 0 15px var(--cyber-pink));
    }
    75% {
        filter: drop-shadow(0 0 10px var(--sigma-gold));
    }
}
