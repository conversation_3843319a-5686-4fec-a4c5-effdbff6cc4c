
:root {
    --dashboard-bg: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 25%, #16213E 50%, #0E4B99 75%, #2E86AB 100%);
    --card-sigma: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    --card-hover: linear-gradient(135deg, rgba(0,212,255,0.1) 0%, rgba(138,43,226,0.1) 100%);
    --stat-gradient-1: linear-gradient(135deg, #2E86AB 0%, #0E4B99 100%);
    --stat-gradient-2: linear-gradient(135deg, #0E4B99 0%, #1A1A2E 100%);
    --stat-gradient-3: linear-gradient(135deg, #1A1A2E 0%, #2E86AB 100%);
    --stat-gradient-4: linear-gradient(135deg, #2E86AB 0%, #0E4B99 100%);
}


body {
    background: var(--dashboard-bg);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(243, 156, 18, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 10%, rgba(255, 20, 147, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 20% 90%, rgba(57, 255, 20, 0.1) 0%, transparent 50%);
    background-size: 300px 300px, 400px 400px, 200px 200px, 350px 350px, 250px 250px;
    animation: particleMove 30s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}


.dashboard-container {
    background: var(--card-sigma);
    backdrop-filter: blur(25px);
    border-radius: 25px;
    padding: 2.5rem;
    margin: 2rem;
    box-shadow:
        0 25px 50px rgba(15, 15, 35, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}


.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00D4FF, #8A2BE2, #FF1493, #F39C12, #39FF14);
    background-size: 400% 100%;
    animation: rainbowFlow 3s ease-in-out infinite;
}


.dashboard-title {
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #00D4FF 0%, #8A2BE2 50%, #FF1493 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    animation: titlePulse 4s ease-in-out infinite;
    position: relative;
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #00D4FF, #8A2BE2, #FF1493);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.7);
}

/* Grille des statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Cartes de statistiques */
.stat-card {
    background: var(--card-sigma);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 15px 35px rgba(15, 15, 35, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--card-hover);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(15, 15, 35, 0.4),
        0 0 30px rgba(0, 212, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stat-card:hover::before {
    opacity: 1;
}

/* Icônes des statistiques */
.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 2;
    transition: all 0.4s ease;
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--stat-gradient-1);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--stat-gradient-2);
    box-shadow: 0 0 20px rgba(243, 156, 18, 0.4);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--stat-gradient-3);
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.4);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--stat-gradient-4);
    box-shadow: 0 0 20px rgba(57, 255, 20, 0.4);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
}

/* Contenu des statistiques */
.stat-content {
    position: relative;
    z-index: 2;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    animation: numberGlow 3s ease-in-out infinite;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Section d'actions rapides */
.quick-actions {
    background: var(--card-sigma);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 15px 35px rgba(15, 15, 35, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.quick-actions h3 {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
    background: linear-gradient(135deg, #00D4FF 0%, #8A2BE2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.action-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 15px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.action-btn.primary {
    background: var(--stat-gradient-1);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.action-btn.secondary {
    background: var(--stat-gradient-3);
    color: white;
    box-shadow: 0 8px 25px rgba(138, 43, 226, 0.3);
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.4);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.action-btn:hover::before {
    left: 100%;
}

/* Animations */
@keyframes particleMove {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
    }
}

@keyframes rainbowFlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes titlePulse {
    0%, 100% {
        text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    }
    50% {
        text-shadow: 0 0 50px rgba(0, 212, 255, 0.8), 0 0 70px rgba(138, 43, 226, 0.6);
    }
}

@keyframes numberGlow {
    0%, 100% {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    }
    50% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.6), 0 0 30px rgba(0, 212, 255, 0.4);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .dashboard-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .dashboard-title {
        font-size: 1.5rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 1rem;
    }
}
