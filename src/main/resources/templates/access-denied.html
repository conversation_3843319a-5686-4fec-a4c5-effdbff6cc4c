<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Acc<PERSON></title>
    <link rel="stylesheet" href="/webjars/bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        body {
            background: linear-gradient(135deg, #2E86AB 0%, #0E4B99 50%, #1A1A2E 100%);
            background-attachment: fixed;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .access-denied-container {
            max-width: 600px;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(15, 15, 35, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .access-denied-icon {
            font-size: 5rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .access-denied-title {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #F1F5F9;
        }
        .access-denied-message {
            font-size: 1.1rem;
            color: #CBD5E1;
            margin-bottom: 30px;
        }
        .btn-group {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <i class="bi bi-shield-lock-fill access-denied-icon"></i>
        <h1 class="access-denied-title">Accès Refusé</h1>
        <div class="access-denied-message">
            <p>Vous n'avez pas les autorisations nécessaires pour accéder à cette page.</p>
            <p>Veuillez vous connecter avec un compte disposant des droits appropriés ou contacter l'administrateur.</p>
        </div>
        <div class="btn-group">
            <a href="/login" class="btn btn-primary me-2">
                <i class="bi bi-box-arrow-in-right me-2"></i>Se connecter
            </a>
            <button onclick="window.history.back()" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>Retour à la page précédente
            </button>
        </div>
    </div>
</body>
</html>
