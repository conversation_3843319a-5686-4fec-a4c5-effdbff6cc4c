<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Datasets</title>
    <link rel="stylesheet" href="/webjars/bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="/webjars/bootstrap-icons/1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <link rel="stylesheet" href="/css/sidebar-navbar.css">
    <style>
        /* Styles spécifiques aux datasets */
        body {
            background: linear-gradient(135deg, #2E86AB 0%, #0E4B99 50%, #1A1A2E 100%);
            background-attachment: fixed;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(46, 134, 171, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(14, 75, 153, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(26, 26, 46, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .main-content {
            padding: 1.5rem 1rem;
        }

        .dataset-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
            max-width: 1100px;
            margin: 0 auto;
        }

        .dataset-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            height: 100%;
            box-shadow:
                0 15px 30px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.25);
            position: relative;
            overflow: hidden;
        }

        .dataset-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            transition: all 0.3s ease;
        }

        .dataset-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .dataset-card:hover {
            transform: translateY(-6px) scale(1.01);
            background: rgba(255, 255, 255, 0.2);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.18),
                0 0 0 1px rgba(255, 255, 255, 0.35),
                0 0 30px rgba(46, 134, 171, 0.15);
        }

        .dataset-card:hover::before {
            height: 4px;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
        }

        .dataset-card::before.add {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
        }

        .dataset-card::before.list {
            background: linear-gradient(135deg, #0E4B99, #1A1A2E);
        }

        .dataset-card::before.assign {
            background: linear-gradient(135deg, #1A1A2E, #2E86AB);
        }

        .dataset-card.add::before {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
        }

        .dataset-card.list::before {
            background: linear-gradient(135deg, #0E4B99, #1A1A2E);
        }

        .dataset-card.assign::before {
            background: linear-gradient(135deg, #1A1A2E, #2E86AB);
        }

        .dataset-card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
            animation: iconPulse 3s ease-in-out infinite;
        }

        .dataset-card-icon.add {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
            color: white;
            box-shadow: 0 8px 16px rgba(46, 134, 171, 0.3);
        }

        .dataset-card-icon.list {
            background: linear-gradient(135deg, #0E4B99, #1A1A2E);
            color: white;
            box-shadow: 0 8px 16px rgba(14, 75, 153, 0.3);
        }

        .dataset-card-icon.assign {
            background: linear-gradient(135deg, #1A1A2E, #2E86AB);
            color: white;
            box-shadow: 0 8px 16px rgba(26, 26, 46, 0.3);
        }

        .dataset-card-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: #F1F5F9;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .dataset-card-text {
            color: #CBD5E1;
            margin-bottom: 1.5rem;
            flex-grow: 1;
            font-size: 0.95rem;
            line-height: 1.6;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
            font-weight: 500;
        }

        .dataset-card-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.6rem 1.2rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .dataset-card-link.add {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
            color: white;
        }

        .dataset-card-link.add:hover {
            background: linear-gradient(135deg, #5BA3C7, #2E86AB);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(46, 134, 171, 0.3);
        }

        .dataset-card-link.list {
            background: linear-gradient(135deg, #0E4B99, #1A1A2E);
            color: white;
        }

        .dataset-card-link.list:hover {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(14, 75, 153, 0.3);
        }

        .dataset-card-link.assign {
            background: linear-gradient(135deg, #1A1A2E, #2E86AB);
            color: white;
        }

        .dataset-card-link.assign:hover {
            background: linear-gradient(135deg, #0E4B99, #5BA3C7);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(26, 26, 46, 0.3);
        }

        .dataset-card-link i {
            margin-right: 0.5rem;
        }

        /* Animation d'apparition */
        .fadeIn {
            animation: fadeInUp 0.4s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Animation de pulsation pour les icônes */
        @keyframes iconPulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Animation décalée pour les cartes */
        .dataset-card:nth-child(1) { animation-delay: 0s; }
        .dataset-card:nth-child(2) { animation-delay: 0.1s; }
        .dataset-card:nth-child(3) { animation-delay: 0.2s; }

        /* Animation décalée pour les icônes */
        .dataset-card:nth-child(1) .dataset-card-icon { animation-delay: 0.2s; }
        .dataset-card:nth-child(2) .dataset-card-icon { animation-delay: 0.3s; }
        .dataset-card:nth-child(3) .dataset-card-icon { animation-delay: 0.4s; }

        /* Responsive */
        @media (max-width: 1024px) {
            .dataset-cards {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1.25rem;
            }
        }

        @media (max-width: 768px) {
            .dataset-cards {
                grid-template-columns: 1fr;
                gap: 1.25rem;
                padding: 0 0.5rem;
            }

            .dataset-card {
                padding: 1.25rem;
            }

            .dataset-card-icon {
                width: 45px;
                height: 45px;
                font-size: 1.1rem;
            }

            .dataset-card-title {
                font-size: 1.1rem;
            }

            .dataset-card-text {
                font-size: 0.85rem;
            }

            .main-content {
                padding: 1rem 0.5rem;
            }
        }
    </style>
</head>
<body>
<div class="app-container">
    <!-- Sidebar -->
    <div class="app-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="material-icons">dashboard</i>
                <span>Annotation App</span>
            </div>
            <button id="sidebar-toggle" class="sidebar-toggle">
                <i class="material-icons">chevron_left</i>
            </button>
        </div>
        <div class="sidebar-menu">
            <ul>
                <li>
                    <a href="/admin/admin">
                        <i class="material-icons">dashboard</i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="active">
                    <a href="/admin/Dataset">
                        <i class="material-icons">storage</i>
                        <span>Datasets</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/listeAnnotateur">
                        <i class="material-icons">people</i>
                        <span>Annotateurs</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/statistics">
                        <i class="material-icons">analytics</i>
                        <span>Statistiques</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="avatar">A</div>
                <div class="user-details">
                    <div class="user-name">Admin</div>
                    <div class="user-email"><EMAIL></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Navbar -->
        <div class="app-navbar">
            <div class="navbar-left">
                <button id="menu-toggle" class="menu-toggle">
                    <i class="material-icons">menu</i>
                </button>
                <h2 class="page-title">Gestion des Datasets</h2>
            </div>
            <div class="navbar-right">
                <div class="navbar-item user-dropdown">
                    <div class="avatar">A</div>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">
                            <i class="material-icons">person</i>
                            <span>Profil</span>
                        </a>
                        <a href="/logout" class="dropdown-item">
                            <i class="material-icons">exit_to_app</i>
                            <span>Déconnexion</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="dataset-cards">
                <div class="dataset-card add fadeIn">
                    <div class="dataset-card-icon add">
                        <i class="bi bi-plus-lg"></i>
                    </div>
                    <h2 class="dataset-card-title">Ajouter un Dataset</h2>
                    <p class="dataset-card-text">Créez un nouveau dataset en important un fichier CSV ou Excel contenant des paires de textes à annoter.</p>
                    <a href="/admin/addDataset" class="dataset-card-link add">
                        <i class="bi bi-plus-lg"></i> Ajouter un Dataset
                    </a>
                </div>

                <div class="dataset-card list fadeIn">
                    <div class="dataset-card-icon list">
                        <i class="bi bi-table"></i>
                    </div>
                    <h2 class="dataset-card-title">Liste des Datasets</h2>
                    <p class="dataset-card-text">Consultez, modifiez ou supprimez les datasets existants et gérez leurs couples de textes pour l'annotation.</p>
                    <a href="/admin/listDatasets" class="dataset-card-link list">
                        <i class="bi bi-list-ul"></i> Voir la Liste
                    </a>
                </div>

                <div class="dataset-card assign fadeIn">
                    <div class="dataset-card-icon assign">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <h2 class="dataset-card-title">Affecter des Tâches</h2>
                    <p class="dataset-card-text">Attribuez des tâches aux annotateurs en sélectionnant un dataset et en assignant des couples de texte avec une date limite.</p>
                    <a href="/admin/assignTasks" class="dataset-card-link assign">
                        <i class="bi bi-person-check"></i> Affecter des Tâches
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="/webjars/bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const appContainer = document.querySelector('.app-container');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-collapsed');
            });
        }

        // Menu toggle (mobile)
        const menuToggle = document.getElementById('menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-mobile-open');
            });
        }

        // User dropdown
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            userDropdown.addEventListener('click', function() {
                this.classList.toggle('open');
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.user-dropdown') && document.querySelector('.user-dropdown.open')) {
                document.querySelector('.user-dropdown.open').classList.remove('open');
            }
        });
    });
</script>
</body>
</html>
