<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Annotateurs</title>
    <link rel="stylesheet" href="/webjars/bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="/webjars/bootstrap-icons/1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <link rel="stylesheet" href="/css/sidebar-navbar.css">
    <style>
        /* Styles spécifiques aux annotateurs */
        /* Styles de base */
        body {
            background: linear-gradient(135deg, #2E86AB 0%, #0E4B99 50%, #1A1A2E 100%);
            background-attachment: fixed;
            min-height: 100vh;
            position: relative;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(46, 134, 171, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(14, 75, 153, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(26, 26, 46, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Styles Sigma pour la liste des annotateurs */
        .annotateurs-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem;
            box-shadow:
                0 25px 50px rgba(15, 15, 35, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .annotateurs-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00D4FF, #8A2BE2, #FF1493, #F39C12);
            background-size: 400% 100%;
            animation: rainbowFlow 3s ease-in-out infinite;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #F1F5F9;
            margin-bottom: 2rem;
            text-align: center;
        }

        /* Card Sigma */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(15, 15, 35, 0.1);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(15, 15, 35, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #F1F5F9, #E2E8F0);
            border-bottom: 1px solid rgba(203, 213, 225, 0.3);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: #475569;
        }

        .card-header i {
            color: #0E4B99;
            margin-right: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Table Sigma */
        .table {
            margin-bottom: 0;
            border-radius: 12px;
            overflow: hidden;
        }

        .table th {
            background: linear-gradient(135deg, #E2E8F0, #CBD5E1);
            font-weight: 600;
            color: #475569;
            border-top: none;
            border-bottom: 2px solid #94A3B8;
            padding: 1rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-color: #E2E8F0;
            color: #475569;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(14, 75, 153, 0.05), rgba(46, 134, 171, 0.05));
            transform: scale(1.01);
        }

        /* Buttons Sigma */
        .btn-ajouter {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #0E4B99, #2E86AB);
            color: white;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(14, 75, 153, 0.3);
            border: none;
        }

        .btn-ajouter:hover {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(14, 75, 153, 0.4);
        }

        .btn-edit {
            background: linear-gradient(135deg, #F39C12, #E67E22);
            color: white;
            border: none;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #E67E22, #D35400);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        .btn-delete {
            background: linear-gradient(135deg, #FF6B6B, #EE5A52);
            color: white;
            border: none;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #EE5A52, #E74C3C);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .btn-primary {
            background: linear-gradient(135deg, #0E4B99, #2E86AB);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2E86AB, #0E4B99);
            transform: translateY(-1px);
        }

        /* Pagination */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-item .page-link {
            color: #4361ee;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #0E4B99, #2E86AB);
            border-color: #0E4B99;
            color: white;
        }

        /* Animations */
        @keyframes rainbowFlow {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }
    </style>
</head>
<body>
<div class="app-container">
    <!-- Sidebar -->
    <div class="app-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="material-icons">dashboard</i>
                <span>Annotation App</span>
            </div>
            <button id="sidebar-toggle" class="sidebar-toggle">
                <i class="material-icons">chevron_left</i>
            </button>
        </div>
        <div class="sidebar-menu">
            <ul>
                <li>
                    <a href="/admin/admin">
                        <i class="material-icons">dashboard</i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/Dataset">
                        <i class="material-icons">storage</i>
                        <span>Datasets</span>
                    </a>
                </li>
                <li class="active">
                    <a href="/admin/listeAnnotateur">
                        <i class="material-icons">people</i>
                        <span>Annotateurs</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/statistics">
                        <i class="material-icons">analytics</i>
                        <span>Statistiques</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="avatar">A</div>
                <div class="user-details">
                    <div class="user-name">Admin</div>
                    <div class="user-email"><EMAIL></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Navbar -->
        <div class="app-navbar">
            <div class="navbar-left">
                <button id="menu-toggle" class="menu-toggle">
                    <i class="material-icons">menu</i>
                </button>
                <h2 class="page-title">Gestion des Annotateurs</h2>
            </div>
            <div class="navbar-right">
                <div class="navbar-item user-dropdown">
                    <div class="avatar">A</div>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">
                            <i class="material-icons">person</i>
                            <span>Profil</span>
                        </a>
                        <a href="/logout" class="dropdown-item">
                            <i class="material-icons">exit_to_app</i>
                            <span>Déconnexion</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="annotateurs-container">
                <h1 class="page-title">
                    <i class="bi bi-people-fill me-2"></i>
                    Gestion des Annotateurs
                </h1>
            <!-- Alerte pour afficher le mot de passe généré -->
            <div th:if="${generatedPassword != null}" class="alert alert-success alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle-fill fs-4 me-3"></i>
                    <div>
                        <strong>Annotateur ajouté avec succès!</strong>
                        <p class="mb-0" th:if="${newUser != null}">
                            L'annotateur <span th:text="${newUser.prenom + ' ' + newUser.nom}" class="fw-bold"></span>
                            a été créé avec le login <span th:text="${newUser.login}" class="fw-bold"></span>
                            et le mot de passe <span th:text="${generatedPassword}" class="fw-bold text-danger"></span>.
                        </p>
                        <p class="mb-0 mt-2 small text-muted">Veuillez noter ce mot de passe, il ne sera plus affiché ultérieurement.</p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="bi bi-list-ul me-2"></i> Annotateurs
                </div>
                <div class="card-body">
                    <form method="get" th:action="@{/admin/listeAnnotateur}" class="mb-3">
                        <div class="input-group">
                            <input type="text" name="keyword" class="form-control" placeholder="Rechercher..." th:value="${keyword}">
                            <input type="hidden" name="size" th:value="${size}">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <a th:href="@{/admin/addAnnotateur(keyword=${keyword},size=${size})}" class="btn-ajouter">
                                <i class="bi bi-plus-lg me-1"></i> Ajouter un Annotateur
                            </a>
                            <span class="ms-3 text-muted" th:if="${totalItems > 0}">
                                <i class="bi bi-info-circle"></i>
                                <span th:text="${totalItems}"></span> annotateur(s) trouvé(s)
                            </span>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Prénom</th>
                                    <th>Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:if="${listeAnnotateur.empty}">
                                    <td colspan="4" class="text-center">Aucun annotateur trouvé</td>
                                </tr>
                                <tr th:each="p : ${listeAnnotateur}">
                                    <td th:text="${p.nom}"></td>
                                    <td th:text="${p.prenom}"></td>
                                    <td th:text="${p.login}"></td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a class="btn btn-edit" th:href="@{/admin/editAnnotateur(id=${p.ID},keyword=${keyword},page=${currentPage},size=${size})}">
                                                <i class="bi bi-pencil-fill"></i>
                                            </a>
                                            <a class="btn btn-delete" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet annotateur?')"
                                               th:href="@{/admin/deleteAnnotateur(id=${p.ID},keyword=${keyword},page=${currentPage},size=${size})}">
                                                <i class="bi bi-trash-fill"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-3">
                        <ul class="pagination">
                            <li th:class="${currentPage==0?'page-item disabled':'page-item'}">
                                <a class="page-link" th:href="@{/admin/listeAnnotateur(page=${currentPage-1},size=${size},keyword=${keyword})}">Précédent</a>
                            </li>
                            <li th:each="page,status:${pages}" th:class="${status.index==currentPage?'page-item active':'page-item'}">
                                <a class="page-link" th:href="@{/admin/listeAnnotateur(page=${status.index},size=${size},keyword=${keyword})}" th:text="${status.index+1}"></a>
                            </li>
                            <li th:class="${currentPage==totalPages-1?'page-item disabled':'page-item'}">
                                <a class="page-link" th:href="@{/admin/listeAnnotateur(page=${currentPage+1},size=${size},keyword=${keyword})}">Suivant</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="/webjars/bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const appContainer = document.querySelector('.app-container');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-collapsed');
            });
        }

        // Menu toggle (mobile)
        const menuToggle = document.getElementById('menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-mobile-open');
            });
        }

        // User dropdown
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            userDropdown.addEventListener('click', function() {
                this.classList.toggle('open');
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.user-dropdown') && document.querySelector('.user-dropdown.open')) {
                document.querySelector('.user-dropdown.open').classList.remove('open');
            }
        });
    });
</script>
</body>
</html>
