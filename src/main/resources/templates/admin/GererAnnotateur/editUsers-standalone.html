<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier un Annotateur</title>
    <link rel="stylesheet" href="/webjars/bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="/webjars/bootstrap-icons/1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <link rel="stylesheet" href="/css/sidebar-navbar.css">
    <style>
        /* Styles spécifiques pour modifier un annotateur - Design Sigma Élégant */
        body {
            background: linear-gradient(135deg, #2E86AB 0%, #0E4B99 50%, #1A1A2E 100%);
            background-attachment: fixed;
            min-height: 100vh;
            position: relative;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(46, 134, 171, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(14, 75, 153, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(26, 26, 46, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Container principal Sigma */
        .edit-annotateur-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 2.5rem;
            margin: 2rem;
            box-shadow:
                0 25px 50px rgba(15, 15, 35, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .edit-annotateur-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00D4FF, #8A2BE2, #FF1493, #F39C12);
            background-size: 400% 100%;
            animation: rainbowFlow 3s ease-in-out infinite;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #F1F5F9;
            margin-bottom: 2rem;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-title i {
            margin-right: 0.75rem;
            color: #F39C12;
            font-size: 2.2rem;
        }

        /* Card Sigma pour le formulaire */
        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(15, 15, 35, 0.15);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .form-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(15, 15, 35, 0.2);
        }

        .form-card .card-header {
            background: linear-gradient(135deg, #F1F5F9, #E2E8F0);
            border-bottom: 2px solid rgba(243, 156, 18, 0.1);
            padding: 1.5rem 2rem;
            font-weight: 700;
            font-size: 1.3rem;
            color: #475569;
            position: relative;
            border-radius: 20px 20px 0 0;
        }

        .form-card .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #F39C12, #E67E22, #D35400);
        }

        .form-card .card-header i {
            color: #F39C12;
            margin-right: 0.75rem;
            font-size: 1.4rem;
        }

        /* Form Sigma */
        .form-label {
            font-weight: 600;
            color: #475569;
            margin-bottom: 0.75rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
        }

        .form-label i {
            margin-right: 0.5rem;
            color: #F39C12;
        }

        .form-control {
            border: 2px solid #E2E8F0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #F39C12;
            box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1), 0 0 20px rgba(243, 156, 18, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        .form-control:hover {
            border-color: #E67E22;
            transform: translateY(-1px);
        }

        /* Buttons Sigma */
        .btn-primary {
            background: linear-gradient(135deg, #F39C12, #E67E22);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #E67E22, #D35400);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #94A3B8, #64748B);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(148, 163, 184, 0.3);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #64748B, #475569);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(148, 163, 184, 0.4);
            color: white;
        }

        /* Bouton de retour */
        .back-link {
            display: inline-flex;
            align-items: center;
            color: #CBD5E1;
            text-decoration: none;
            margin-bottom: 1.5rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #F1F5F9;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-3px);
        }

        .back-link i {
            margin-right: 0.5rem;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
            border: 1px solid rgba(243, 156, 18, 0.3);
            color: #B7791F;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        /* Animations */
        @keyframes rainbowFlow {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }
    </style>
</head>
<body>
<div class="app-container">
    <!-- Sidebar -->
    <div class="app-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="material-icons">dashboard</i>
                <span>Annotation App</span>
            </div>
            <button id="sidebar-toggle" class="sidebar-toggle">
                <i class="material-icons">chevron_left</i>
            </button>
        </div>
        <div class="sidebar-menu">
            <ul>
                <li>
                    <a href="/admin/admin">
                        <i class="material-icons">dashboard</i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/Dataset">
                        <i class="material-icons">storage</i>
                        <span>Datasets</span>
                    </a>
                </li>
                <li class="active">
                    <a href="/admin/listeAnnotateur">
                        <i class="material-icons">people</i>
                        <span>Annotateurs</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/statistics">
                        <i class="material-icons">analytics</i>
                        <span>Statistiques</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="avatar">A</div>
                <div class="user-details">
                    <div class="user-name">Admin</div>
                    <div class="user-email"><EMAIL></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Navbar -->
        <div class="app-navbar">
            <div class="navbar-left">
                <button id="menu-toggle" class="menu-toggle">
                    <i class="material-icons">menu</i>
                </button>
                <h2 class="page-title">Modifier un Annotateur</h2>
            </div>
            <div class="navbar-right">
                <div class="navbar-item user-dropdown">
                    <div class="avatar">A</div>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">
                            <i class="material-icons">person</i>
                            <span>Profil</span>
                        </a>
                        <a href="/logout" class="dropdown-item">
                            <i class="material-icons">exit_to_app</i>
                            <span>Déconnexion</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="edit-annotateur-container">
                <h1 class="page-title">
                    <i class="bi bi-pencil-square"></i>
                    Modifier un Annotateur
                </h1>

                <a th:href="@{/admin/listeAnnotateur(page=${page},keyword=${keyword},size=${size})}" class="back-link">
                    <i class="bi bi-arrow-left"></i> Retour à la liste des annotateurs
                </a>

                <div class="card form-card">
                    <div class="card-header">
                        <i class="bi bi-pencil-square"></i> Informations de l'annotateur
                    </div>
                <div class="card-body p-4">
                    <form method="post" th:action="@{/save}" th:object="${user}">
                        <input type="hidden" name="page" th:value="${page}">
                        <input type="hidden" name="keyword" th:value="${keyword}">
                        <input type="hidden" name="size" th:value="${size}">
                        <input type="hidden" name="ID" th:value="${user.ID}">

                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="nom" class="form-label">
                                    <i class="bi bi-person"></i> Nom
                                </label>
                                <input type="text" class="form-control" id="nom" name="nom" th:value="${user.nom}" required>
                                <span class="text-danger" th:errors="${user.nom}"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="prenom" class="form-label">
                                    <i class="bi bi-person-badge"></i> Prénom
                                </label>
                                <input type="text" class="form-control" id="prenom" name="prenom" th:value="${user.prenom}" required>
                                <span class="text-danger" th:errors="${user.prenom}"></span>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="login" class="form-label">
                                <i class="bi bi-at"></i> Login
                            </label>
                            <input type="text" class="form-control" id="login" name="login" th:value="${user.login}" required>
                            <div class="form-text text-muted mt-2">
                                <i class="bi bi-info-circle me-1"></i>
                                Ce login sera utilisé par l'annotateur pour se connecter à l'application.
                            </div>
                            <span class="text-danger" th:errors="${user.login}"></span>
                        </div>

                        <div class="alert alert-info d-flex align-items-center mb-4">
                            <i class="bi bi-info-circle-fill fs-4 me-3"></i>
                            <div>
                                <strong>Information sur le mot de passe</strong>
                                <p class="mb-0">Le mot de passe de cet annotateur reste inchangé. Il conserve le mot de passe qui lui a été attribué lors de sa création.</p>
                            </div>
                        </div>

                        <input type="hidden" name="active" value="true">

                        <div class="d-flex justify-content-between mt-5">
                            <a th:href="@{/admin/listeAnnotateur(page=${page},keyword=${keyword},size=${size})}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="/webjars/bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const appContainer = document.querySelector('.app-container');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-collapsed');
            });
        }

        // Menu toggle (mobile)
        const menuToggle = document.getElementById('menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-mobile-open');
            });
        }

        // User dropdown
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            userDropdown.addEventListener('click', function() {
                this.classList.toggle('open');
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.user-dropdown') && document.querySelector('.user-dropdown.open')) {
                document.querySelector('.user-dropdown.open').classList.remove('open');
            }
        });
    });
</script>
</body>
</html>
