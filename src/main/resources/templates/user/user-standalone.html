<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Annotateur</title>
    <link rel="stylesheet" href="/webjars/bootstrap/5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="/webjars/bootstrap-icons/1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <link rel="stylesheet" href="/css/sidebar-navbar.css">
    <link rel="stylesheet" href="/css/user-dashboard.css">
    <style>
        /* Styles simples et épurés pour l'interface utilisateur */
        body {
            background: linear-gradient(135deg, #2E86AB 0%, #0E4B99 50%, #1A1A2E 100%);
            background-attachment: fixed;
            font-family: 'Inter', sans-serif;
        }

        .simple-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem;
            box-shadow:
                0 25px 50px rgba(15, 15, 35, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .simple-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #F1F5F9;
            text-align: center;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .simple-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .simple-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(15, 15, 35, 0.2);
        }

        .simple-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(15, 15, 35, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .card-icon {
            font-size: 2rem;
            color: #00D4FF;
            margin-bottom: 1rem;
        }

        .card-number {
            font-size: 2rem;
            font-weight: 700;
            color: #F1F5F9;
            margin-bottom: 0.5rem;
        }

        .card-label {
            font-size: 0.9rem;
            color: #CBD5E1;
            font-weight: 500;
        }

        .simple-actions {
            text-align: center;
        }

        .simple-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #0E4B99, #2E86AB);
            color: white;
            padding: 1rem 2rem;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(14, 75, 153, 0.3);
        }

        .simple-btn:hover {
            background: linear-gradient(135deg, #2E86AB, #00D4FF);
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(14, 75, 153, 0.4);
            color: white;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .simple-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .simple-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .simple-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
<div class="app-container">
    <!-- Sidebar -->
    <div class="app-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="material-icons">assignment</i>
                <span>Annotation App</span>
            </div>
            <button id="sidebar-toggle" class="sidebar-toggle">
                <i class="material-icons">chevron_left</i>
            </button>
        </div>
        <div class="sidebar-menu">
            <ul>
                <li class="active">
                    <a href="/user/user">
                        <i class="material-icons">home</i>
                        <span>Accueil</span>
                    </a>
                </li>
                <li>
                    <a href="/user/tasks">
                        <i class="material-icons">assignment</i>
                        <span>Mes Tâches</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Navbar -->
        <div class="app-navbar">
            <div class="navbar-left">
                <button id="menu-toggle" class="menu-toggle">
                    <i class="material-icons">menu</i>
                </button>
                <h2 class="page-title">Tableau de Bord Sigma</h2>
            </div>
            <div class="navbar-right">
                <div class="navbar-item user-dropdown">
                    <div class="avatar" th:text="${annotateur != null ? (annotateur.prenom.substring(0, 1) + annotateur.nom.substring(0, 1)) : 'U'}">U</div>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">
                            <i class="material-icons">person</i>
                            <span>Profil</span>
                        </a>
                        <a href="/logout" class="dropdown-item">
                            <i class="material-icons">exit_to_app</i>
                            <span>Déconnexion</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="simple-container">
                <h1 class="simple-title">
                    <i class="bi bi-person-circle"></i>
                    Espace Annotateur
                </h1>

                <!-- Statistiques Simples -->
                <div class="simple-stats">
                    <div class="simple-card">
                        <div class="card-icon">
                            <i class="bi bi-list-task"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-number" th:text="${totalTaches ?: 0}">0</div>
                            <div class="card-label">Tâches Assignées</div>
                        </div>
                    </div>

                    <div class="simple-card">
                        <div class="card-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-number" th:text="${tachesTerminees ?: 0}">0</div>
                            <div class="card-label">Tâches Terminées</div>
                        </div>
                    </div>

                    <div class="simple-card">
                        <div class="card-icon">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-number" th:text="${tachesEnCours ?: 0}">0</div>
                            <div class="card-label">Tâches En Cours</div>
                        </div>
                    </div>
                </div>

                <!-- Actions Simples -->
                <div class="simple-actions">
                    <a href="/user/tasks" class="simple-btn">
                        <i class="bi bi-play-circle"></i>
                        Commencer à Annoter
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="/webjars/bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const appContainer = document.querySelector('.app-container');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-collapsed');
            });
        }

        // Menu toggle (mobile)
        const menuToggle = document.getElementById('menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                appContainer.classList.toggle('sidebar-mobile-open');
            });
        }

        // User dropdown
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            userDropdown.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (userDropdown && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('active');
            }
        });

        // Interface simplifiée - pas d'animations complexes
        console.log('Interface utilisateur simplifiée chargée');
    });
</script>
</body>
</html>
